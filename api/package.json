{"name": "final-full-stack-mern-advanced-api", "private": true, "version": "1.0.0", "author": "TrungQuanDev - <PERSON><PERSON><PERSON>", "description": "https://youtube.com/@trungquandev", "engines": {"node": ">=18.x"}, "scripts": {"lint": "eslint src --ext js --report-unused-disable-directives --max-warnings 0", "clean": "rm -rf build && mkdir build", "build-babel": "babel ./src -d ./build/src", "build": "npm run clean && npm run build-babel", "production": "npm run build && cross-env BUILD_MODE=production node ./build/src/server.js", "dev": "cross-env BUILD_MODE=dev nodemon --exec babel-node ./src/server.js"}, "dependencies": {"@babel/runtime": "^7.22.10", "@getbrevo/brevo": "^2.0.0-beta.4", "async-exit-hook": "^2.0.1", "bcryptjs": "^2.4.3", "cloudinary": "^2.0.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^16.3.1", "express": "^4.18.2", "http-status-codes": "^2.3.0", "joi": "^17.10.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mongodb": "^6.0.0", "ms": "^2.1.3", "multer": "^1.4.5-lts.1", "nodemailer": "^7.0.3", "socket.io": "^4.7.4", "streamifier": "^0.1.1", "uuid": "^9.0.1"}, "devDependencies": {"@babel/cli": "^7.22.10", "@babel/core": "^7.22.10", "@babel/eslint-parser": "^7.22.10", "@babel/node": "^7.22.10", "@babel/plugin-transform-runtime": "^7.22.10", "@babel/preset-env": "^7.27.2", "babel-plugin-module-resolver": "^5.0.0", "eslint": "^8.47.0", "nodemon": "^3.0.1"}}